# OTA订单处理系统 - AI代理指导

## 项目概览

这是一个基于原生JavaScript的OTA（在线旅游代理）订单处理系统，集成了GoMyHire API和Google Gemini AI，用于智能解析和处理旅游订单。

## 核心架构模式

### 1. 模块化架构（非ES6模块）
- **命名空间**: 所有模块挂载到 `window.OTA` 全局对象
- **加载方式**: 传统script标签，避免ES6模块依赖问题
- **初始化顺序**: 严格按照 `index.html` 中的script标签顺序加载

```javascript
// 模块结构示例
window.OTA = window.OTA || {};
(function() {
    'use strict';
    // 延迟获取依赖，确保加载顺序
    function getAppState() {
        return window.OTA.appState || window.appState;
    }
})();
```

### 2. Manager模式 - 关键架构决策
- **UIManager作为协调器**: `js/ui-manager.js` 管理所有子管理器实例
- **管理器职责分离**: FormManager、StateManager、EventManager、PriceManager等
- **委托模式**: UIManager通过`getManager()`访问子管理器
- **事件驱动**: 状态变化通过AppState监听器触发UI更新

```javascript
// Manager初始化模式
this.managers.form = new window.OTA.managers.FormManager(this.elements);
// 访问管理器的标准方式
const formManager = uiManager.getManager('form');
```

### 3. 状态管理模式
- **集中状态**: `js/app-state.js` 管理所有应用状态
- **状态分类**: auth、systemData、currentOrder、config
- **响应式更新**: 通过状态变更监听器触发UI更新
- **持久化**: localStorage自动保存用户配置和认证信息

### 4. AI解析数据流
关键流程：`输入文本` → `Gemini AI解析` → `状态更新` → `表单填充` → `预览生成`

```javascript
// 数据转换：snake_case (AI) → camelCase (表单)
// 例如：car_type_id → carTypeId
fillFormFromData(data) {
    // 自动处理字段名转换和下拉框填充
}
```

## 关键文件和职责

### 核心模块
- `main.js`: 应用启动和初始化协调器
- `js/app-state.js`: 全局状态管理，包含用户认证和数据缓存
- `js/ui-manager.js`: UI协调器，管理所有子管理器模块
- `js/api-service.js`: GoMyHire API交互，包含静态数据映射
- `js/gemini-service.js`: Google Gemini AI集成和智能解析

### 管理器模块 (`js/managers/`)
- `state-manager.js`: UI状态和主题管理
- `form-manager.js`: 表单处理和验证
- `price-manager.js`: 价格计算和货币转换
- `event-manager.js`: 事件处理和DOM交互
- `realtime-analysis-manager.js`: 实时AI分析功能

### 支持模块
- `js/logger.js`: 分级日志系统（INFO/WARNING/ERROR）
- `js/utils.js`: 工具函数和性能监控
- `js/i18n.js`: 多语言支持（中文/英文）
- `js/ota-channel-mapping.js`: 用户到OTA渠道的配置映射

## 开发约定

### 1. 错误处理和日志
```javascript
// 始终使用logger模块记录操作
const logger = getLogger();
logger.log('操作成功', 'info', { data });
logger.logError('操作失败', error);
```

### 2. 异步操作模式
```javascript
// API调用统一使用try-catch
try {
    const result = await apiService.createOrder(orderData);
    // 处理成功结果
} catch (error) {
    logger.logError('创建订单失败', error);
    // 显示用户友好的错误信息
}
```

### 3. DOM元素缓存模式
```javascript
// UI管理器中统一缓存DOM元素
this.elements = {
    orderInput: document.getElementById('orderInput'),
    otaChannel: document.getElementById('ota'), // 注意：ID是'ota'不是'otaChannel'
    parseBtn: document.getElementById('parseBtn')
};
```

### 4. 用户特定配置模式
- **OTA渠道配置**: `js/ota-channel-mapping.js` 根据用户ID或邮箱映射专属渠道
- **配置优先级**: 用户专属配置 > 通用配置
- **自动填充**: 登录成功后自动设置用户专属OTA渠道，移除占位符选项

```javascript
// OTA渠道自动配置示例 (form-manager.js)
populateOtaChannelOptions() {
    const user = getAppState().get('auth.user');
    const otaConfig = window.OTA.otaChannelMapping.getConfig(user.id || user.email);
    
    if (otaConfig && otaConfig.default) {
        // 有专属配置：直接设置并选中，不显示占位符
        this.elements.otaChannel.value = otaConfig.default;
    } else {
        // 无专属配置：显示占位符和通用选项
        // 添加占位符逻辑
    }
}
```

## 修复模式和故障排除

### 1. 初始化时序问题
- **问题**: 管理器初始化顺序导致功能失效
- **解决**: 在 `main.js` 的 `initializeModules()` 中显式调用每个管理器的 `init()` 方法
- **检查**: 确保 `updateLoginUI()` 调用了 `populateOtaChannelOptions()`

### 2. 状态监听器问题
- **问题**: 登录成功后UI未正确更新
- **解决**: 检查 `state-manager.js` 中的 `auth.isLoggedIn` 监听器是否正确调用表单填充
- **关键**: 状态管理器必须调用 `formManager.populateOtaChannelOptions()` 而不仅仅是 `populateFormOptions()`

### 3. OTA渠道配置问题
- **问题**: 特定用户登录后OTA渠道显示不正确
- **诊断**: 检查 `ota-channel-mapping.js` 中的用户配置和 `getConfig()` 方法
- **修复**: 确保邮箱匹配使用 `toLowerCase()` 且配置正确映射

## 部署和构建

### Netlify部署
- **无构建步骤**: 纯静态文件部署
- **配置文件**: `netlify.toml` 包含安全头和缓存设置
- **环境**: 生产环境直接使用原生JavaScript

### 开发调试
- **日志输出**: 开发者控制台，支持分级过滤
- **状态页面**: `/status.html` 显示系统状态和API连接
- **测试页面**: `test-*.html` 用于功能验证

## API集成要点

### GoMyHire API
- **认证**: Bearer token，自动刷新机制
- **静态映射**: 避免频繁API调用，提高性能
- **错误处理**: 统一的API错误响应处理

### Google Gemini AI
- **实时分析**: 1.5秒防抖，最小20字符触发
- **智能ID填充**: 基于api return id list.md数据自动匹配
- **错误恢复**: 失败时保持用户输入不丢失

## 记忆库和文档

项目使用 `memory-bank/` 目录存储架构文档：
- `project-structure.md`: 项目结构说明
- `code_structure.md`: AI解析链路分析
- 更新功能时请同步更新相关文档

遵循中文注释规范，为重要代码添加说明性注释。
