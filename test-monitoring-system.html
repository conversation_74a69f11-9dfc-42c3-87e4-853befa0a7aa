<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局监控系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
            margin: 10px 0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        .test-section h2 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 1.4em;
        }
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .btn.success {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }
        .btn.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        .btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #b8daff;
        }
        .controls {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            color: white;
        }
        .controls h3 {
            margin: 0 0 15px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.green {
            background: #2ecc71;
        }
        .status-indicator.red {
            background: #e74c3c;
        }
        .status-indicator.yellow {
            background: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 全局监控系统测试</h1>
            <p>测试OTA订单处理系统的全局监控功能</p>
        </div>

        <div class="controls">
            <h3>监控控制台</h3>
            <button class="btn success" onclick="enableMonitoring()">启用监控</button>
            <button class="btn warning" onclick="disableMonitoring()">禁用监控</button>
            <button class="btn" onclick="showMonitoringReport()">显示监控报告</button>
            <button class="btn" onclick="clearMonitoringData()">清除监控数据</button>
            <button class="btn danger" onclick="testErrorGeneration()">测试错误生成</button>
        </div>

        <div class="test-section">
            <h2><span class="status-indicator" id="factoryStatus"></span>工厂函数监控测试</h2>
            <button class="btn" onclick="testFactoryFunctions()">测试工厂函数调用</button>
            <button class="btn" onclick="performanceTestFactoryFunctions()">性能压力测试</button>
            <div id="factoryResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2><span class="status-indicator" id="performanceStatus"></span>性能监控测试</h2>
            <button class="btn" onclick="testPerformanceMonitoring()">测试性能监控</button>
            <button class="btn" onclick="simulateSlowOperation()">模拟慢操作</button>
            <div id="performanceResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2><span class="status-indicator" id="interactionStatus"></span>用户交互监控测试</h2>
            <button class="btn" onclick="testUserInteractions()">测试用户交互</button>
            <button class="btn" onclick="simulateFormInput()">模拟表单输入</button>
            <div id="interactionResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2><span class="status-indicator" id="systemStatus"></span>系统状态监控测试</h2>
            <button class="btn" onclick="testSystemStateChanges()">测试状态变化</button>
            <button class="btn" onclick="simulateNetworkChange()">模拟网络状态变化</button>
            <div id="systemResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2><span class="status-indicator" id="errorStatus"></span>错误跟踪测试</h2>
            <button class="btn" onclick="testErrorTracking()">测试错误跟踪</button>
            <button class="btn danger" onclick="triggerTestError()">触发测试错误</button>
            <div id="errorResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 实时监控数据</h2>
            <button class="btn" onclick="showLiveStats()">显示实时统计</button>
            <button class="btn" onclick="exportMonitoringData()">导出监控数据</button>
            <div id="statsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 加载核心模块 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/i18n.js"></script>

    <script>
        // 等待模块加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟确保所有模块都已加载
            setTimeout(initializeMonitoringTest, 1000);
        });

        function initializeMonitoringTest() {
            console.log('🚀 初始化监控测试系统...');
            
            // 检查监控系统状态
            updateStatusIndicators();
            
            // 启用调试模式以查看详细监控信息
            if (window.OTA && window.OTA.logger) {
                window.OTA.logger.setDebugMode(true);
                console.log('✅ 调试模式已启用');
            }
            
            console.log('🎯 监控测试系统已就绪！');
            console.log('💡 打开开发者工具控制台查看监控信息');
            console.log('📋 使用 monitoring.report() 查看监控报告');
        }

        function updateStatusIndicators() {
            const indicators = {
                factory: checkFactoryFunctionMonitoring(),
                performance: checkPerformanceMonitoring(),
                interaction: checkUserInteractionMonitoring(),
                system: checkSystemStateMonitoring(),
                error: checkErrorTracking()
            };

            Object.entries(indicators).forEach(([key, status]) => {
                const indicator = document.getElementById(`${key}Status`);
                if (indicator) {
                    indicator.className = `status-indicator ${status ? 'green' : 'red'}`;
                }
            });
        }

        function checkFactoryFunctionMonitoring() {
            return window.OTA && window.OTA.monitoringWrapper && 
                   typeof window.getAppState === 'function';
        }

        function checkPerformanceMonitoring() {
            return 'PerformanceObserver' in window && 
                   window.OTA && window.OTA.logger && window.OTA.logger.monitoring;
        }

        function checkUserInteractionMonitoring() {
            return window.OTA && window.OTA.logger && 
                   window.OTA.logger.monitoring && window.OTA.logger.monitoring.userInteractions;
        }

        function checkSystemStateMonitoring() {
            return window.OTA && window.OTA.logger && 
                   window.OTA.logger.monitoring && window.OTA.logger.monitoring.systemStates;
        }

        function checkErrorTracking() {
            return window.OTA && window.OTA.logger && 
                   window.OTA.logger.monitoring && window.OTA.logger.monitoring.errorTracking;
        }

        // 监控控制函数
        function enableMonitoring() {
            if (window.OTA && window.OTA.logger) {
                window.OTA.logger.setMonitoringEnabled(true);
                updateStatusIndicators();
                showResult('success', '✅ 监控系统已启用');
            } else {
                showResult('error', '❌ Logger服务未找到');
            }
        }

        function disableMonitoring() {
            if (window.OTA && window.OTA.logger) {
                window.OTA.logger.setMonitoringEnabled(false);
                updateStatusIndicators();
                showResult('warning', '⚠️ 监控系统已禁用');
            } else {
                showResult('error', '❌ Logger服务未找到');
            }
        }

        function showMonitoringReport() {
            if (window.monitoring && window.monitoring.report) {
                const report = window.monitoring.report();
                showResult('info', '📊 监控报告已在控制台显示，详细数据请查看开发者工具');
            } else {
                showResult('error', '❌ 监控报告功能未找到');
            }
        }

        function clearMonitoringData() {
            if (window.monitoring && window.monitoring.clear) {
                window.monitoring.clear();
                showResult('success', '🗑️ 监控数据已清除');
            } else {
                showResult('error', '❌ 清除功能未找到');
            }
        }

        function testErrorGeneration() {
            try {
                throw new Error('这是一个测试错误 - 用于验证错误跟踪功能');
            } catch (error) {
                if (window.OTA && window.OTA.logger) {
                    window.OTA.logger.logError(error, { testContext: '错误跟踪测试' });
                }
                showResult('warning', '⚠️ 测试错误已生成并记录');
            }
        }

        // 工厂函数测试
        function testFactoryFunctions() {
            const results = [];
            const factoryFunctions = [
                'getAppState',
                'getGeminiService',
                'getAPIService',
                'getImageUploadManager',
                'getCurrencyConverter',
                'getMultiOrderManager',
                'getI18nManager'
            ];

            factoryFunctions.forEach(funcName => {
                try {
                    const start = performance.now();
                    const result = window[funcName] && window[funcName]();
                    const duration = performance.now() - start;
                    
                    results.push(`✅ ${funcName}: ${duration.toFixed(2)}ms - ${result ? '成功' : '失败'}`);
                } catch (error) {
                    results.push(`❌ ${funcName}: ERROR - ${error.message}`);
                }
            });

            showResult('info', results.join('\n'), 'factoryResult');
        }

        function performanceTestFactoryFunctions() {
            const iterations = 100;
            const results = [];
            
            console.time('工厂函数性能测试');
            
            for (let i = 0; i < iterations; i++) {
                try {
                    window.getAppState && window.getAppState();
                    window.getGeminiService && window.getGeminiService();
                    window.getAPIService && window.getAPIService();
                } catch (error) {
                    console.error('性能测试错误:', error);
                }
            }
            
            console.timeEnd('工厂函数性能测试');
            
            showResult('info', `🏃‍♂️ 完成 ${iterations} 次工厂函数调用\n检查控制台查看详细性能数据`, 'factoryResult');
        }

        // 性能监控测试
        function testPerformanceMonitoring() {
            performance.mark('test-start');
            
            // 模拟一些操作
            setTimeout(() => {
                performance.mark('test-end');
                performance.measure('test-operation', 'test-start', 'test-end');
                
                showResult('success', '⚡ 性能标记已创建，检查控制台查看性能数据', 'performanceResult');
            }, 100);
        }

        function simulateSlowOperation() {
            performance.mark('slow-operation-start');
            
            // 模拟耗时操作
            const start = Date.now();
            while (Date.now() - start < 200) {
                // 忙等待200ms
            }
            
            performance.mark('slow-operation-end');
            performance.measure('slow-operation', 'slow-operation-start', 'slow-operation-end');
            
            showResult('warning', '🐌 慢操作已完成，应该会触发性能警告', 'performanceResult');
        }

        // 用户交互测试
        function testUserInteractions() {
            // 模拟点击事件
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            document.body.dispatchEvent(event);
            
            showResult('success', '👆 模拟用户点击已触发，检查控制台查看交互记录', 'interactionResult');
        }

        function simulateFormInput() {
            // 创建临时输入框并触发事件
            const input = document.createElement('input');
            input.type = 'text';
            input.value = '测试输入';
            document.body.appendChild(input);
            
            const inputEvent = new Event('input', { bubbles: true });
            input.dispatchEvent(inputEvent);
            
            const changeEvent = new Event('change', { bubbles: true });
            input.dispatchEvent(changeEvent);
            
            document.body.removeChild(input);
            
            showResult('success', '📝 模拟表单输入已触发', 'interactionResult');
        }

        // 系统状态测试
        function testSystemStateChanges() {
            if (window.OTA && window.OTA.logger) {
                window.OTA.logger.logSystemStateChange('test-state', 'active', '测试状态变化');
                window.OTA.logger.logSystemStateChange('user-preference', 'dark-mode', '用户偏好变更');
                
                showResult('success', '🔄 系统状态变化已记录', 'systemResult');
            } else {
                showResult('error', '❌ Logger服务未找到', 'systemResult');
            }
        }

        function simulateNetworkChange() {
            // 模拟网络状态变化事件
            const onlineEvent = new Event('online');
            const offlineEvent = new Event('offline');
            
            window.dispatchEvent(offlineEvent);
            setTimeout(() => {
                window.dispatchEvent(onlineEvent);
            }, 1000);
            
            showResult('info', '🌐 网络状态变化已模拟（离线→在线）', 'systemResult');
        }

        // 错误跟踪测试
        function testErrorTracking() {
            if (window.OTA && window.OTA.logger) {
                const errorCount = window.OTA.logger.monitoring.errorTracking.size;
                showResult('info', `📊 当前跟踪的错误类型: ${errorCount} 种\n检查控制台查看详细错误统计`, 'errorResult');
            } else {
                showResult('error', '❌ Logger服务未找到', 'errorResult');
            }
        }

        function triggerTestError() {
            // 触发不同类型的错误
            setTimeout(() => {
                throw new Error('定时器中的测试错误');
            }, 10);
            
            Promise.reject(new Error('Promise拒绝测试错误')).catch(() => {
                // 故意不处理，让它成为未处理的拒绝
            });
            
            showResult('danger', '💥 多种类型的测试错误已触发', 'errorResult');
        }

        // 实时统计
        function showLiveStats() {
            if (window.OTA && window.OTA.logger) {
                const stats = window.OTA.logger.getStats();
                const factoryStats = window.OTA.logger.getFactoryFunctionStats();
                
                const statsText = `
📊 系统统计 (${new Date().toLocaleTimeString()})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📝 日志统计:
  总数: ${stats.total}
  今日: ${stats.today}
  最近1小时: ${stats.lastHour}

🏭 工厂函数调用:
  总调用次数: ${factoryStats.totalCalls}
  最慢函数: ${factoryStats.slowestFunction || '无'}
  最常调用: ${factoryStats.mostCalledFunction || '无'}

📈 按级别分布:
${Object.entries(stats.byLevel).map(([level, count]) => `  ${level}: ${count}`).join('\n')}

🔧 按类型分布:
${Object.entries(stats.byType).map(([type, count]) => `  ${type}: ${count}`).join('\n')}
                `;
                
                showResult('info', statsText, 'statsResult');
            } else {
                showResult('error', '❌ Logger服务未找到', 'statsResult');
            }
        }

        function exportMonitoringData() {
            if (window.monitoring && window.monitoring.export) {
                window.monitoring.export('json');
                showResult('success', '📄 监控数据已导出为JSON文件', 'statsResult');
            } else {
                showResult('error', '❌ 导出功能未找到', 'statsResult');
            }
        }

        // 辅助函数
        function showResult(type, message, targetId = null) {
            const result = targetId ? 
                document.getElementById(targetId) : 
                document.querySelector('.result:last-of-type') || 
                document.createElement('div');
            
            if (!targetId) {
                result.className = 'result';
                document.querySelector('.test-section:last-of-type').appendChild(result);
            }
            
            result.className = `result ${type}`;
            result.textContent = message;
            result.style.display = 'block';
            
            // 同时在控制台输出
            console.log(`[监控测试] ${message}`);
        }
    </script>
</body>
</html>
