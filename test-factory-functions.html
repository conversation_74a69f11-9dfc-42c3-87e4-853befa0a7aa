<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工厂函数测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>🔧 工厂函数修复验证测试</h1>
    <div id="testResults"></div>

    <!-- 加载所有必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/paging-service-manager.js"></script>
    <script src="js/i18n.js"></script>

    <script>
        // 测试函数
        function runTests() {
            const results = document.getElementById('testResults');
            
            function addResult(name, success, message) {
                const div = document.createElement('div');
                div.className = `test-result ${success ? 'success' : 'error'}`;
                div.innerHTML = `<strong>${success ? '✅' : '❌'} ${name}</strong>: ${message}`;
                results.appendChild(div);
            }

            function addInfo(message) {
                const div = document.createElement('div');
                div.className = 'test-result info';
                div.innerHTML = `<strong>ℹ️ 信息</strong>: ${message}`;
                results.appendChild(div);
            }

            addInfo('开始工厂函数修复验证测试...');

            // 测试核心服务工厂函数
            try {
                const appState1 = getAppState();
                const appState2 = getAppState();
                addResult('AppState工厂函数', 
                    typeof getAppState === 'function' && appState1 === appState2,
                    `工厂函数存在且返回单例实例`);
            } catch (e) {
                addResult('AppState工厂函数', false, `错误: ${e.message}`);
            }

            try {
                const gemini1 = getGeminiService();
                const gemini2 = getGeminiService();
                addResult('GeminiService工厂函数', 
                    typeof getGeminiService === 'function' && gemini1 === gemini2,
                    `工厂函数存在且返回单例实例`);
            } catch (e) {
                addResult('GeminiService工厂函数', false, `错误: ${e.message}`);
            }

            try {
                const api1 = getAPIService();
                const api2 = getAPIService();
                addResult('APIService工厂函数', 
                    typeof getAPIService === 'function' && api1 === api2,
                    `工厂函数存在且返回单例实例`);
            } catch (e) {
                addResult('APIService工厂函数', false, `错误: ${e.message}`);
            }

            // 测试进阶功能工厂函数
            const advancedServices = [
                { name: '图片上传管理器', func: 'getImageUploadManager' },
                { name: '货币转换器', func: 'getCurrencyConverter' },
                { name: '多订单管理器', func: 'getMultiOrderManager' },
                { name: '举牌服务管理器', func: 'getPagingServiceManager' },
                { name: '历史订单管理器', func: 'getOrderHistoryManager' },
                { name: '国际化管理器', func: 'getI18nManager' }
            ];

            advancedServices.forEach(service => {
                try {
                    const func = window[service.func];
                    if (typeof func === 'function') {
                        const instance1 = func();
                        const instance2 = func();
                        addResult(service.name, 
                            instance1 === instance2,
                            `工厂函数存在且返回单例实例`);
                    } else {
                        addResult(service.name, false, `工厂函数不存在`);
                    }
                } catch (e) {
                    addResult(service.name, false, `错误: ${e.message}`);
                }
            });

            // 测试监控系统
            addInfo('测试全局监控系统...');
            
            try {
                const monitoringWrapper = window.OTA && window.OTA.monitoringWrapper;
                addResult('监控包装器', 
                    Boolean(monitoringWrapper),
                    monitoringWrapper ? '监控包装器已加载' : '监控包装器未找到');
                
                if (monitoringWrapper) {
                    const stats = monitoringWrapper.getWrapperStats();
                    addResult('已包装函数数量', 
                        stats.wrappedFunctionsCount > 0,
                        `已包装 ${stats.wrappedFunctionsCount} 个函数: ${stats.wrappedFunctions.join(', ')}`);
                }
            } catch (e) {
                addResult('监控包装器', false, `错误: ${e.message}`);
            }

            try {
                const logger = window.OTA && window.OTA.logger;
                const hasMonitoring = logger && logger.monitoring && logger.monitoring.enabled;
                addResult('监控系统', 
                    hasMonitoring,
                    hasMonitoring ? '监控系统已启用' : '监控系统未启用');
                
                if (hasMonitoring) {
                    addResult('控制台命令', 
                        typeof window.monitoring === 'object',
                        window.monitoring ? '监控控制台命令已设置' : '监控控制台命令未设置');
                }
            } catch (e) {
                addResult('监控系统', false, `错误: ${e.message}`);
            }

            // 测试命名空间一致性
            addInfo('检查命名空间一致性...');
            
            const namespaceTests = [
                { name: 'window.OTA.appState', path: 'window.OTA.appState' },
                { name: 'window.OTA.getAppState', path: 'window.OTA.getAppState' },
                { name: 'window.OTA.geminiService', path: 'window.OTA.geminiService' },
                { name: 'window.OTA.getGeminiService', path: 'window.OTA.getGeminiService' },
                { name: 'window.OTA.apiService', path: 'window.OTA.apiService' },
                { name: 'window.OTA.getAPIService', path: 'window.OTA.getAPIService' },
                { name: 'window.OTA.logger', path: 'window.OTA.logger' },
                { name: 'window.OTA.monitoringWrapper', path: 'window.OTA.monitoringWrapper' }
            ];

            namespaceTests.forEach(test => {
                try {
                    const exists = eval(test.path) !== undefined;
                    addResult(`命名空间 ${test.name}`, exists, exists ? '存在' : '不存在');
                } catch (e) {
                    addResult(`命名空间 ${test.name}`, false, `错误: ${e.message}`);
                }
            });

            // 性能测试
            addInfo('执行性能测试...');
            
            try {
                performance.mark('factory-test-start');
                
                // 多次调用工厂函数测试性能
                for (let i = 0; i < 50; i++) {
                    if (window.getAppState) window.getAppState();
                    if (window.getGeminiService) window.getGeminiService();
                    if (window.getAPIService) window.getAPIService();
                }
                
                performance.mark('factory-test-end');
                performance.measure('factory-test-duration', 'factory-test-start', 'factory-test-end');
                
                const measure = performance.getEntriesByName('factory-test-duration')[0];
                const avgTime = measure.duration / 50;
                
                addResult('工厂函数性能', 
                    avgTime < 1,
                    `50次调用平均耗时: ${avgTime.toFixed(3)}ms`);
                
                // 清理性能标记
                performance.clearMarks();
                performance.clearMeasures();
                
            } catch (e) {
                addResult('工厂函数性能', false, `性能测试失败: ${e.message}`);
            }

            addInfo('测试完成！');
            
            // 如果监控系统可用，显示监控报告
            if (window.monitoring && window.monitoring.report) {
                addInfo('生成监控报告（查看控制台）...');
                setTimeout(() => {
                    window.monitoring.report();
                }, 1000);
            }
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 1000); // 延迟1秒确保所有脚本加载完成
        });
    </script>
</body>
</html>
