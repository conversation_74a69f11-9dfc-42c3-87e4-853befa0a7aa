<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Table Fix Test</title>
</head>
<body>
    <h1>Console Table Fix Test</h1>
    <p>Check the browser console for test results.</p>
    <button onclick="testTableFunctionality()">Test Table Functionality</button>

    <script src="js/logger.js"></script>
    <script>
        function testTableFunctionality() {
            console.log('🧪 Testing console.table fix...');
            
            // Create logger instance
            const logger = new Logger();
            
            // Test data
            const testData = {
                function1: { calls: 5, avgTime: '12ms', errors: 0 },
                function2: { calls: 3, avgTime: '8ms', errors: 1 },
                function3: { calls: 10, avgTime: '15ms', errors: 0 }
            };
            
            const testArray = [
                { name: 'test1', value: 100 },
                { name: 'test2', value: 200 },
                { name: 'test3', value: 300 }
            ];
            
            console.log('✅ Testing with object data:');
            logger.safeTableCall('Test Object Data', testData);
            
            console.log('✅ Testing with array data:');
            logger.safeTableCall('Test Array Data', testArray);
            
            console.log('✅ Testing with null data:');
            logger.safeTableCall('Test Null Data', null);
            
            console.log('✅ Testing with undefined data:');
            logger.safeTableCall('Test Undefined Data', undefined);
            
            console.log('✅ Testing originalConsole.table directly:');
            try {
                logger.originalConsole.table(testData);
                console.log('✅ Direct table call succeeded');
            } catch (error) {
                console.log('⚠️ Direct table call failed, but that\'s expected in some environments');
            }
            
            console.log('🎉 Console table fix test completed!');
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testTableFunctionality, 1000);
        });
    </script>
</body>
</html>
